import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess, Net_Code } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { ActivityTakeResponse, TimeTaskMessage, TimeTaskTakeRequest } from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { ActivityID } from "../activity/ActivityConstant";
import { TimeTaskModule } from "./TimeTaskModule";
import { TimeTaskApiCallback, TimeTaskApiErrorCallback, TakeRewardRequest } from "./TimeTaskTypes";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

/**
 * 限时任务API类
 * 负责与服务器进行网络通信，处理任务相关的数据交互
 */
export class TimeTaskApi {
  private static readonly REQUEST_TIMEOUT = 10000; // 10秒超时
  private _requestQueue: Map<string, number> = new Map(); // 请求队列，防止重复请求

  /**
   * 获取限时任务信息
   * 路由: 14 - 35 --- 【获取限时任务信息】 --- 【ActivityAction:950】【timeTaskInfo】
   * @param activityId 活动ID，默认为TIME_TASK_1
   * @param success 成功回调
   * @param error 失败回调
   */
  public getTimeTaskInfo(
    activityId: number = ActivityID.TIME_TASK_1,
    success?: TimeTaskApiCallback<TimeTaskMessage>,
    error?: TimeTaskApiErrorCallback
  ): void {
    const requestKey = `timeTaskInfo_${activityId}`;

    // 防止重复请求
    if (this._requestQueue.has(requestKey)) {
      log.warn(`重复请求限时任务信息: ${activityId}`);
      return;
    }

    this._requestQueue.set(requestKey, Date.now());

    log.log(`请求限时任务信息: activityId=${activityId}`);

    ApiHandler.instance.requestSync(
      TimeTaskMessage,
      ActivityCmd.timeTaskInfo,
      LongValue.encode({ value: activityId }),
      (data: TimeTaskMessage) => {
        this._requestQueue.delete(requestKey);

        if (!data) {
          log.error("获取限时任务信息失败: 服务器返回空数据");
          error && error(-1, "服务器返回空数据");
          return;
        }

        log.log("获取限时任务信息成功:", data);

        // 更新本地数据
        TimeTaskModule.data.setTimeTaskMessage(data);

        // 执行成功回调
        success && success(data);
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        this._requestQueue.delete(requestKey);

        log.error(`获取限时任务信息失败: errorCode=${errorCode}, msg=${msg}`);
        error && error(errorCode, msg.join(", "), data);
      }
    );
  }

  /**
   * 领取限时任务奖励
   * 路由: 14 - 36 --- 【领取限时任务完成奖励】 --- 【ActivityAction:970】【takeTimeTask】
   * @param request 领取奖励请求参数
   * @param success 成功回调
   * @param error 失败回调
   */
  public takeTimeTaskReward(
    request: TakeRewardRequest,
    success?: TimeTaskApiCallback<ActivityTakeResponse>,
    error?: TimeTaskApiErrorCallback
  ): void {
    const requestKey = `takeReward_${request.activityId}_${request.taskId}_${request.index}`;

    // 防止重复请求
    if (this._requestQueue.has(requestKey)) {
      log.warn(`重复请求领取奖励: ${JSON.stringify(request)}`);
      return;
    }

    // 参数验证
    if (!this._validateTakeRewardRequest(request)) {
      error && error(-1, "请求参数无效");
      return;
    }

    this._requestQueue.set(requestKey, Date.now());

    const serverRequest: TimeTaskTakeRequest = {
      activityId: request.activityId,
      taskId: request.taskId,
      index: request.index,
      takeAll: request.takeAll,
    };

    log.log(`请求领取奖励:`, serverRequest);

    ApiHandler.instance.requestSync(
      ActivityTakeResponse,
      ActivityCmd.takeTimeTask,
      TimeTaskTakeRequest.encode(serverRequest),
      (data: ActivityTakeResponse) => {
        this._requestQueue.delete(requestKey);

        if (!data) {
          log.error("领取奖励失败: 服务器返回空数据");
          error && error(-1, "服务器返回空数据");
          return;
        }

        log.log("领取奖励成功:", data);

        // 更新本地数据
        TimeTaskModule.data.updateTakeList(request.taskId, data.takeList);

        // 执行成功回调
        success && success(data);
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        this._requestQueue.delete(requestKey);

        log.error(`领取奖励失败: errorCode=${errorCode}, msg=${msg}`);
        error && error(errorCode, msg.join(", "), data);
      }
    );
  }

  /**
   * 批量领取奖励
   * @param activityId 活动ID
   * @param taskId 任务ID
   * @param success 成功回调
   * @param error 失败回调
   */
  public takeAllRewards(
    activityId: number,
    taskId: number,
    success?: TimeTaskApiCallback<ActivityTakeResponse>,
    error?: TimeTaskApiErrorCallback
  ): void {
    const request: TakeRewardRequest = {
      activityId,
      taskId,
      index: 0, // 批量领取时索引为0
      takeAll: true,
    };

    this.takeTimeTaskReward(request, success, error);
  }

  /**
   * 验证领取奖励请求参数
   */
  private _validateTakeRewardRequest(request: TakeRewardRequest): boolean {
    if (!request) {
      log.error("请求参数为空");
      return false;
    }

    if (!request.activityId || request.activityId <= 0) {
      log.error("活动ID无效:", request.activityId);
      return false;
    }

    if (!request.taskId || request.taskId <= 0) {
      log.error("任务ID无效:", request.taskId);
      return false;
    }

    if (!request.takeAll && request.index < 0) {
      log.error("奖励索引无效:", request.index);
      return false;
    }

    return true;
  }

  /**
   * 清理过期的请求
   */
  public cleanExpiredRequests(): void {
    const now = Date.now();
    for (const [key, timestamp] of this._requestQueue.entries()) {
      if (now - timestamp > TimeTaskApi.REQUEST_TIMEOUT) {
        this._requestQueue.delete(key);
        log.warn(`清理过期请求: ${key}`);
      }
    }
  }

  /**
   * 获取当前请求队列状态
   */
  public getRequestQueueStatus(): { [key: string]: number } {
    const status: { [key: string]: number } = {};
    for (const [key, timestamp] of this._requestQueue.entries()) {
      status[key] = Date.now() - timestamp;
    }
    return status;
  }
}
